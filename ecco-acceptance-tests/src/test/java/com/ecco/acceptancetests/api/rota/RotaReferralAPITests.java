package com.ecco.acceptancetests.api.rota;

import com.ecco.acceptancetests.api.BaseJsonTest;
import com.ecco.acceptancetests.steps.RotaSteps;
import com.ecco.acceptancetests.ui.pages.Role;
import com.ecco.dom.CustomEventAbstract;
import com.ecco.webApi.calendar.ServiceRecipientRotaDecorator;
import com.ecco.data.client.ReferralOptions;
import com.ecco.dom.agreements.DaysOfWeek;
import com.ecco.infrastructure.time.Clock;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.rota.webApi.dto.Rota;
import com.ecco.rota.webApi.dto.RotaAppointmentViewModel;
import com.ecco.rota.webApi.dto.RotaResourceViewModel;
import com.ecco.webApi.calendar.CalendarEntryCommandViewModel;
import com.ecco.webApi.calendar.ServiceRecipientCalendarEntryCommandViewModel;
import com.ecco.webApi.contacts.address.AddressViewModel;
import com.ecco.webApi.evidence.ReferralSummaryViewModel;
import com.ecco.webApi.rota.AppointmentActionCommandDto;
import com.ecco.webApi.viewModels.Result;
import com.ecco.calendar.core.Recurrence;
import kotlin.Pair;
import org.assertj.core.api.Assertions;
import org.assertj.core.api.Condition;
import org.jspecify.annotations.Nullable;
import org.joda.time.DateTime;
import org.junit.jupiter.api.Test;
import org.springframework.web.client.HttpServerErrorException;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Objects;

import static com.ecco.data.client.ServiceOptions.DEMO_ALL;
import static com.ecco.dto.ChangeViewModel.changeNullTo;
import static com.ecco.infrastructure.time.JodaToJDKAdapters.localDateToJDk;
import static com.ecco.webApi.evidence.BaseCommandViewModel.OPERATION_ADD;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;
import static org.junit.Assert.*;

/**
 * Rota endToEnd test for appointments to workers.
 * Also see RotaScheduleAPITests.
 * <ol>
 *     <li>create user + worker and link (or link existing user if being clever)</li>
 *     <li>create availability</li>
 *     <li>create client</li>
 *     <li>create appointment</li>
 *     <li>create worker availability</li>
 *     <li>check can assign appt to worker</li>
 * </ol>
 */
public class RotaReferralAPITests extends BaseJsonTest {

    private Clock clock = Clock.DEFAULT;
    private org.joda.time.DateTime now = clock.now();
    private java.time.ZonedDateTime nowJdk = clock.nowJdk();

    // also need rota permission

    // TODO
    //   BLDG ROTA - configSetupBuildingRota - create bldg adr, client with adr, staff with adr (dynamic/secure/hr/worker/overview.html?workerId=100002),
    //   rotaSteps should use a context similar to ReferralOptions to pass data around so that the tests look much cleaner

    // NB also see ECCO-SNIPPETS 'ROTA - config'
    //  after this test, do this
    //  select * from bldg_fixed order by serviceRecipientId desc;
    //  select clientId from referrals order by serviceRecipientId desc;
    //  select residenceId from clientdetails where id=100220;
    //  update clientdetails set residenceId=1399 where id=100220;
    //  rota day next month, Mon, Tues, Wed
    //  add availability with the pattern

    // TODO WHOLE ROTA
    //  after this test, do this:
    //  add rota permission (NB - whole org feature is on, rota module is on)
    @Test
    public void configSetupWholeRotaBasicScenarios() {
        loginAsSysadmin();

        // module table has rota true by default

        // worker with user
        String userName = unique.userNameFor("staff-setup1");
        String pwd = "staff";
        userManagementSteps.createIndividualWithUser(userName, pwd, userName + "-first", userName + "-last", Role.staff);
        rotaSteps.createWorkerAndJobWithAvailabilityForToday(userName, null);

        // worker with user
        userName = unique.userNameFor("staff-setup2");
        pwd = "staff";
        userManagementSteps.createIndividualWithUser(userName, pwd, userName + "-first", userName + "-last", Role.staff);
        rotaSteps.createWorkerAndJobWithAvailabilityForToday(userName, null);

        // doesn't need to be accepted
        var r1 = referralSteps.processReferral(new ReferralOptions().withNoWorkflow(), DEMO_ALL,
                "Setup", "RotaClient1", "*********", "this is a unique referral comment for Setup RotaClient1");
        ReferralSummaryViewModel rsvm1 = referralActor.getReferralSummaryById(r1.getFirst()).getBody();
        assert rsvm1 != null;
        var firstInMonthAt7 = nowJdk.toLocalDateTime().with(TemporalAdjusters.firstDayOfMonth()).withHour(7).withMinute(0).withSecond(0).withNano(0);
        agreementActor.createAgreement(rsvm1.serviceRecipientId, JodaToJDKAdapters.localDateToJoda(firstInMonthAt7.toLocalDate()), JodaToJDKAdapters.localDateToJoda(firstInMonthAt7.plusDays(90).toLocalDate()), null);

        // doesn't need to be accepted
        var r2 = referralSteps.processReferral(new ReferralOptions().withNoWorkflow(), DEMO_ALL,
                "Setup", "RotaClient2", "*********", "this is a unique referral comment for Setup RotaClient2");
        ReferralSummaryViewModel rsvm2 = referralActor.getReferralSummaryById(r2.getFirst()).getBody();
        assert rsvm2 != null;
        var firstInNextMonthAt8 = nowJdk.plusMonths(1).toLocalDateTime().with(TemporalAdjusters.firstDayOfMonth()).withHour(8).withMinute(0).withSecond(0).withNano(0);
        agreementActor.createAgreement(rsvm2.serviceRecipientId, JodaToJDKAdapters.localDateToJoda(firstInNextMonthAt8.toLocalDate()), JodaToJDKAdapters.localDateToJoda(firstInNextMonthAt8.plusDays(90).toLocalDate()), null);

        // schedule for all this month
        var lastInMonth = nowJdk.toLocalDate().with(TemporalAdjusters.lastDayOfMonth());
        createRecurringSchedule(r1, 0, firstInMonthAt7, null, lastInMonth, null);

        // schedule for all this month - Mon/Wed/Fri
        var lastInNextMonth = nowJdk.plusMonths(1).toLocalDate().with(TemporalAdjusters.lastDayOfMonth());
        createRecurringSchedule(r2, 0, firstInNextMonthAt8, DaysOfWeek.fromStringDays(new String[]{"mon", "tues", "wed"}), lastInNextMonth, null);

        logout();
    }

    @Test
    public void endToEndTest() {
        loginAsSysadmin();

        var referralPair = referralSteps.processReferral(new ReferralOptions().withNoWorkflow(), DEMO_ALL,
                "First-endToEnd", "Last-endToEnd", "*********", "this is a unique referral comment 1");
        var referral = referralActor.getReferralSummaryById(referralPair.getFirst()).getBody();
        assert referral != null;

        // 'adHocSchedule(referralPair);' removed here in d8ae8817 as a way to clarify a test

        var firstDayInMonth = nowJdk.toLocalDate().with(TemporalAdjusters.firstDayOfMonth());
        agreementActor.createAgreement(referral.serviceRecipientId, JodaToJDKAdapters.localDateToJoda(firstDayInMonth), null, null);

        testRecurringSchedule(referralPair, 0, null, null, clock.nowJdk().toLocalDate().plusMonths(2), null);

        // CHANGE ADDRESS
        var avm = new AddressViewModel();
        avm.setLine1(unique.appendId("line1-uniq"));
        avm.setPostcode("SE3 0ND");
        avm.setTown("Origin");
        changeAddress(referral, avm);
        // CHANGE ADDRESS

        testRecurringSchedule(referralPair, 1, null, null, clock.nowJdk().toLocalDate().plusMonths(2), null);

        // CHECK ADDRESS
        // cached
        var sr = serviceRecipientActor.findSummaryCached(referral.serviceRecipientId).getBody();
        assertThat("cached address not updated", sr.addressCommaSep, is(avm.toCommaSepString()));
        // rota from cache
        Rota rota = rotaActor.getWholeOrgRotaOnDate(LocalDate.now().atTime(11, 0));
        var apt = rota.findDemandByServiceRecipientName(referralPair.getSecond()).get(0);
        assertThat("cached address not updated", sr.addressCommaSep, is(apt.getLocation()));
        // CHECK ADDRESS

        logout();
    }

    private void changeAddress(ReferralSummaryViewModel vm, AddressViewModel avm) {
        var newAdrId = Integer.parseInt(addressActor.createAddress(avm).getBody().getId());
        addressActor.changeAddress(newAdrId, vm.serviceRecipientId, vm.contactId.intValue());
    }

    /**
     * Found a scenario where editing a schedule twice causes an apt hangover.
     * 	- create schedule from today-3days every day with time 10am
     * 	- split schedule applicable at today+2days with time 10.10am
     * 	- allocate new schedule recurring (from today+2days) to someone
     * 	- split new schedule at its start (applicable at today+2days) with time 10.20am
     * 	- rota on today+2 should be new schedule unallocated (not an additional allocated from previous split which has end < start)
     */
    @Test
    public void scheduleEditTwice_noExtraAppt() {

    }

    /**
     * Found a scenario where recur deallocate can truncate a schedule.
     *  - schedule for Fri/Sat/Sun
     *  - allocate Fri recurring
     *  - move a few weeks on
     *  - deallocate Fri recurring
     *  - Fri deallocated, but Sat/Sun no longer show!!
     *  NB The key to the issue vs the ui is that the days are not present on recur deallocate.
     *  Therefore, the solution for now would be to add the day, but ideally we fix null.
     */
    @Test
    public void recurDeallocate() {
        loginAsSysadmin();

        // CREATE referral
        ReferralSummaryViewModel referral;
        Pair<Long, String> referralPair;
        {
            referralPair = referralSteps.processReferral(new ReferralOptions().withNoWorkflow(), DEMO_ALL,
                    "First-DeallocR", "Last-DeallocR", "AB654321E", "this is a unique referral comment 1");
            referral = referralActor.getReferralSummaryById(referralPair.getFirst()).getBody();
            assert referral != null;
        }

        // CREATE worker
        RotaSteps.WorkerResult worker;
        {
            String userName = unique.userNameFor("staff-deallocR");
            String pwd = "staff";
            userManagementSteps.createIndividualWithUser(userName, pwd, userName + "-first", userName + "-last", Role.staff);
            worker = rotaSteps.createWorkerAndJobWithAvailabilityForToday(userName, null);
        }

        // CREATE schedule
        var daysForSchedule = DaysOfWeek.fromStringDays(new String[]{"fri", "sat", "sun"});
        var firstFriday = nowJdk.with(TemporalAdjusters.next(DayOfWeek.FRIDAY));
        var firstFriAt11am = firstFriday.toLocalDateTime().withHour(11).withMinute(0).withSecond(0).withNano(0);
        {
            var firstDayInMonth = nowJdk.toLocalDate().with(TemporalAdjusters.firstDayOfMonth());
            agreementActor.createAgreement(referral.serviceRecipientId, JodaToJDKAdapters.localDateToJoda(firstDayInMonth), null, null);
            createRecurringSchedule(referralPair, 0, firstFriAt11am, daysForSchedule, null, null);
        }

        // THEN allocate recurring on second Fri (NB first apt is 11am) for the whole schedule
        var daysToAllocate = DaysOfWeek.fromStringDays(new String[]{"fri"});
        var secondFriday = firstFriday.plusWeeks(1);
        var secondFriAt11am = secondFriday.toLocalDateTime().withHour(11).withMinute(0).withSecond(0).withNano(0);
        {
            // specify a time to ensure the date is used (if it's not today)
            rotaSteps.checkCanAssignResourceToRecurringAppointmentAtTime("workers:all", "referrals:all", referral.displayName, worker.getName(),
                    secondFriAt11am, daysToAllocate, null);

            // check assigned
            Rota futureFridayRota = rotaActor.getWholeOrgRotaOnDate(secondFriday.toLocalDateTime());
            checkRotaAllocated(futureFridayRota, referralPair, worker, referral);
        }

        // THEN deallocate Fri only, recurring, from a few weeks
        // such that we have start Fri (first) -> allocated Fri (second) -> deallocated Fri (third)
        DaysOfWeek daysToDeallocate = null; // DaysOfWeek.fromStringDays(new String[]{"fri"});
        var futureFriday = nowJdk.with(TemporalAdjusters.next(DayOfWeek.FRIDAY)).plusWeeks(3);
        {
            var futureFriAt11am = futureFriday.toLocalDateTime().withHour(11).withMinute(0).withSecond(0).withNano(0);
            // ** daysToDeallocate is null in the ui - key to repeating the issue here
            rotaSteps.checkCanUnAssignResourceFromRecurringAppointmentAtTime("workers:all", "referrals:all", referral.displayName, worker.getName(),
                    futureFriAt11am, daysToDeallocate, null);

            // check the second Fri is still allocated
            Rota secondFridayRota = rotaActor.getWholeOrgRotaOnDate(secondFriday.toLocalDateTime());
            checkRotaAllocated(secondFridayRota, referralPair, worker, referral);
            Rota futureFridayRota = rotaActor.getWholeOrgRotaOnDate(futureFriday.toLocalDateTime());
            checkRotaUnassigned(futureFridayRota, referralPair, worker, referral);
        }

        // TEST we have a Sat/Sun well into the future
        Rota futureSatRota = rotaActor.getWholeOrgRotaOnDate(futureFriday.plusDays(1).toLocalDateTime());

        // ** this is what fails for the bug - there is no apt on Sat (or Sun) **
        checkRotaUnassigned(futureSatRota, referralPair, worker, referral);

        logout();
    }

    private static void checkRotaAllocated(Rota rotaDate, Pair<Long, String> referralPair, RotaSteps.WorkerResult worker, ReferralSummaryViewModel referral) {
        var unallocatedAppointments = rotaDate.findDemandByServiceRecipientName(referralPair.getSecond());
        var allocatedAppointments = rotaDate.findResourceByName(worker.getName()).findAppointmentsByServiceRecipientName(referral.displayName);

        assertThat("Expected NO appointment on the day", unallocatedAppointments, hasSize(0));
        assertThat("Expected one appointment allocated on the day", allocatedAppointments, hasSize(1));
        assertThat("Expected one appointment to be CONFIRMED", unallocatedAppointments.stream().filter(a -> !Recurrence.Status.CONFIRMED.equals(a.getStatus())).count(), is(0L));
        //Condition<? super RotaAppointmentViewModel> correctEndDate =
        //        new Condition<>(a -> a.getEnd().toLocalDate().equals(localDateToJDk(expectedEndDate)), "correct expectedEndDate date");
        //Assertions.assertThat(matches.get(0).getAppointments()).first().has(correctEndDate);
    }

    private static void checkRotaUnassigned(Rota rotaDate, Pair<Long, String> referralPair, RotaSteps.WorkerResult worker, ReferralSummaryViewModel referral) {
        var unallocatedAppointments = rotaDate.findDemandByServiceRecipientName(referralPair.getSecond());
        var allocatedAppointments = rotaDate.findResourceByName(worker.getName()).findAppointmentsByServiceRecipientName(referral.displayName);

        assertThat("Expected one appointment on the day", unallocatedAppointments, hasSize(1));
        assertThat("Expected NO appointment allocated on the day", allocatedAppointments, hasSize(0));
        assertThat("Expected NO appointment to be CONFIRMED", unallocatedAppointments.stream().filter(a -> Recurrence.Status.CONFIRMED.equals(a.getStatus())).count(), is(0L));
    }

    // found a scenario in the series calendar on recurring allocate (day 0), deallocate (day +1) and re-allocate (day +2),
    // where there appear to be DST issues in that the actions are on the day after, there is also an issue of 'start is invalid: 2022-05-19T14:39:36.000Z is after 2022-05-17'
    // and we find that day+1 shows both pending and allocated
    // this could possibly be in CosmoCalendarSeriesIntegrationTest.java
    // FAILED on actions/runs/2806842659 6th Aug without any real changes
    @Test
    public void noEndDate_reallocate() {
        // NB ASSUMES no end dates is configured
        // it is the default calendar with integration, but api tests need server with -Decco.calendar.series=true

        loginAsSysadmin();

        // WHEN have a schedule
        ReferralSummaryViewModel referral;
        String workerName;
        //var daysToAllocate = IntStream.rangeClosed(now.getDayOfWeek(), now.getDayOfWeek()+3)
        //        .map(d -> d > 7 ? d-7 : d).toArray();
        var daysToAllocate = DaysOfWeek.fromCalendarDaysISO(List.of(1,2,3,4,5,6,7));
        {
            String userName = unique.userNameFor("staff-scen1");
            String pwd = "staff";
            userManagementSteps.createIndividualWithUser(userName, pwd, userName + "-first", userName + "-last", Role.staff);
            workerName = rotaSteps.createWorkerAndJobWithAvailabilityForToday(userName, null).getName();

            var r1 = referralSteps.processReferral(new ReferralOptions().withNoWorkflow(), DEMO_ALL,
                    "Setup", "RotaScen1", "*********", "this is a unique referral comment for Setup RotaScen1");
            referral = referralActor.getReferralSummaryById(r1.getFirst()).getBody();
            assert referral != null;
            var firstDayInMonth = nowJdk.toLocalDate().with(TemporalAdjusters.firstDayOfMonth());
            agreementActor.createAgreement(referral.serviceRecipientId, JodaToJDKAdapters.localDateToJoda(firstDayInMonth), null, null);

            var todayAt11am = nowJdk.toLocalDateTime().withHour(11).withMinute(0).withSecond(0).withNano(0);
            // this triggers the RotaDemandChangeAgent to create some concrete recurrences
            createRecurringSchedule(r1, 0, todayAt11am, daysToAllocate, clock.nowJdk().toLocalDate().plusMonths(2), null);
        }

        // THEN allocate recurring from today (NB first apt is todayAt11am) for the whole schedule
        {
            // allocating from today covers the schedule - so 'matchesOneExactly' is called but it's trying to clear data
            // which is referenced by cal_eventstatus, and so we get data integrity error:
            //      ConstraintViolationException: could not execute batch [Cannot delete or update a parent row: a foreign key constraint fails (`acctest`.`cal_eventstatus`, CONSTRAINT `FK_eventstatus_eventuid` FOREIGN KEY (`eventUid`) REFERENCES `cosmo_item` (`item_uid`))]","links":null}"
            rotaSteps.checkCanAssignResourceToRecurringAppointmentAtTime("workers:all", "referrals:all", referral.displayName, workerName,
                    null, daysToAllocate, null);
        }

        // THEN deallocate recurring from tomorrow
        {
            var tomorrowAt11am = nowJdk.plusDays(1).toLocalDateTime().withHour(11).withMinute(0).withSecond(0).withNano(0);
            rotaSteps.checkCanUnAssignResourceFromRecurringAppointmentAtTime("workers:all", "referrals:all", referral.displayName, workerName,
                    tomorrowAt11am, daysToAllocate, null);
        }

        // TODO check there isn't an allocation and demand for the same thing... the series end date should be correct

        // WHEN allocate recurring from day after
        {
            var dayAfterAt11am = nowJdk.toLocalDateTime().plusDays(2).withHour(11).withMinute(0).withSecond(0).withNano(0);
            rotaSteps.checkCanAssignResourceToRecurringAppointmentAtTime("workers:all", "referrals:all", referral.displayName, workerName,
                    dayAfterAt11am, daysToAllocate, null);
        }

        // so lets check there is nothing pending on the second day
        Rota rota = rotaActor.getWholeOrgRotaOnDate(now.plusDays(1));
        // demand expected since we unallocated from this day
        List<RotaAppointmentViewModel> pendingAppointments = rota.findDemandByServiceRecipientName(referral.displayName);
        assertThat("Expect appointment returned", pendingAppointments, iterableWithSize(1));
        // allocation NOT expected since we unallocated from this day
        List<RotaAppointmentViewModel> allocatedAppointments = rota.findResourceByName(workerName).findAppointmentsByServiceRecipientName(referral.displayName);
        assertThat("Expect no appointment returned", allocatedAppointments, iterableWithSize(0));

        logout();
    }

    private void testRecurringSchedule(Pair<Long, String> referralPair, int additionalStaff, @Nullable LocalDateTime start, @Nullable DaysOfWeek daysOfWeek, @Nullable LocalDate endDate, @Nullable LocalDateTime testRecurrence) {

        createRecurringSchedule(referralPair, additionalStaff, start, daysOfWeek, endDate, testRecurrence);

        // drop found appointment in 2 weeks' time from schedule
        var testDateTime = testRecurrence != null ? testRecurrence : LocalDate.now().atTime(11, 0);
        Rota rota = rotaActor.getWholeOrgRotaOnDate(testDateTime);
        var apts = rota.findDemandByServiceRecipientName(referralPair.getSecond());
        var apt = apts.get(0);

        // in case of additionalStaff, avoid the parent schedule because we can't delete the parent below
        if (additionalStaff > 0) {
            // parent will be the lowest/first id
            var parentScheduleId = apts.stream()
                    .flatMap(a -> a.getLinks(ServiceRecipientRotaDecorator.REL_DEMAND_SCHEDULE_ADDITIONAL).stream())
                    .map(l -> l.getHref().replaceAll("[^0-9]", ""))
                    .sorted()
                    .findFirst()
                    .orElseThrow();
            apt = apts.stream().filter(a -> !a.getManagedByUri().toString().contains(parentScheduleId)).findFirst().orElseThrow();
        }

        var testRecurrenceJoda = JodaToJDKAdapters.localDateTimeToJoda(testDateTime).toDateTime();
        checkCanDropAppointmentFromSchedule(apt, testRecurrenceJoda);

        // reinstate dropped appointment in 2 weeks' time to schedule
        checkCanReinstateAppointmentToSchedule(apt, testRecurrenceJoda);

        // delete schedule
        checkCanDeleteSchedule(apt, additionalStaff);
    }

    /**
     * Create a schedule.
     *
     * @param referralPair referral
     * @param additionalStaff extra workers
     * @param start Provided, or 11am today
     * @param daysOfWeek Provided, or the day of start
     * @param endDate Provided, or defaulted in checkCanCreateScheduleOnFirstAgreement to 2 months
     */
    private void createRecurringSchedule(Pair<Long, String> referralPair, int additionalStaff, @Nullable LocalDateTime start, @Nullable DaysOfWeek daysOfWeek, @Nullable LocalDate endDate, @Nullable LocalDateTime testRecurrence) {
        Long referralId = referralPair.getFirst();
        String clientName = referralPair.getSecond();
        ReferralSummaryViewModel svm = referralActor.getReferralSummaryById(referralId).getBody();
        assert svm != null;

        // SCHEDULE
        // test non ad-hoc, since ad-hoc is tested above via createClientUnfulfilledAdhocAppointmentDemandCreateOnFirstAgreement
        // GIVEN a repeating schedule on the day of 'now' every week for 2 months duration 10mins
        LocalDateTime schedTime = start != null ? start : LocalDate.now().atTime(11, 0);
        var scheduleAgreementId = Long.parseLong(rotaSteps.checkCanCreateScheduleOnFirstAgreement(clientName, svm.serviceRecipientId, schedTime, daysOfWeek, endDate, additionalStaff, null).getId());

        DateTime randomApt = testRecurrence != null
                ? JodaToJDKAdapters.localDateTimeToJoda(testRecurrence).toDateTime()
                : JodaToJDKAdapters.localDateTimeToJoda(schedTime.plusWeeks(1)).toDateTime();
        if (testRecurrence == null && daysOfWeek != null) {
            int currDay = randomApt.getDayOfWeek();
            randomApt = randomApt.minusDays(currDay).plusDays(daysOfWeek.toCalendarDayListISO().get(0));
        }

        Rota rota = rotaActor.getWholeOrgRotaOnDate(randomApt);
        assertThat("Expected appointment returned", rota.findDemandByServiceRecipientName(clientName), iterableWithSize(1 + additionalStaff));

        // Check the appointment has some links
        // NB the worker doesn't actually own any events - its put onto the client or run
        var rotaVisitLink = rota.findDemandByServiceRecipientName(clientName).get(0).getLink(ServiceRecipientRotaDecorator.REL_ROTAVISIT);
        assertTrue("appointment should have 'rota visit'", rotaVisitLink.isPresent());

        var additionalStaffLinks = rota.findDemandByServiceRecipientName(clientName).get(0).getLinks(ServiceRecipientRotaDecorator.REL_DEMAND_SCHEDULE_ADDITIONAL);
        assertEquals("appointment should have 'demand-schedule-additional'", additionalStaffLinks.size(), additionalStaff);
        // test the additional staff event links is not the parents event link
        if (additionalStaff > 0) {
            var apt = rota.findDemandByServiceRecipientName(clientName).get(0);
            var additionalStaffEvents = apt.getLinks(ServiceRecipientRotaDecorator.REL_DEMAND_SCHEDULE_ADDITIONAL_EVENTS);
            var clientOwnerCalendarId = svm.calendarId;
            assertTrue("client calendarId should not be in additionalStaff calendarIds", additionalStaffEvents.stream().noneMatch(e -> e.getHref().indexOf(clientOwnerCalendarId) > 0));
        }

    }

    // NB extracted from end-to-end - see "DEV-2510 Reinstate adHoc test"
    // see also RotaScheduleAPITests#scheduleWithAllocate
    @Test
    public void adHocScheduleWithAllocate() {
        loginAsSysadmin();

        var referralPair = referralSteps.processReferral(new ReferralOptions().withNoWorkflow(), DEMO_ALL,
                "First-adHoc", "Last-adHoc", "*********", "this is a unique referral comment 1");
        var referral = referralActor.getReferralSummaryById(referralPair.getFirst()).getBody();
        assert referral != null;
        String clientName = referralPair.getSecond();
        Long referralId = referralPair.getFirst();

        String userName = unique.userNameFor("Worker-adHoc");
        String pwd = unique.passwordFor("Worker-adHoc");
        userManagementSteps.createIndividualWithUser(userName, pwd,
                userName + "-first", userName + "-last", Role.staff);
        var workerIdName = rotaSteps.createWorkerAndJobWithAvailabilityForToday(userName, null);
        var workerName = workerIdName.getName();
        LocalDateTime adHocTime = LocalDate.now().atTime(22, 0);
        // allocates to resourceSrId
        Long adHocAgreementId = rotaSteps.createClientUnfulfilledAdhocAppointmentDemandCreateFirstAgreement(userName, referralPair, adHocTime, workerIdName.getServiceRecipientId());
        Rota rotaAdHoc = rotaActor.getWholeOrgRotaOnDate(adHocTime);

        // check we find allocated appt and it matches the setup
        RotaAppointmentViewModel adHocApt;
        {
            // demand is 0
            assertThat("Expect no demand for this client as it's allocated", rotaAdHoc.findDemandByServiceRecipientName(clientName).size(), equalTo(0));
            // resource has the demand (first entry)
            adHocApt = rotaAdHoc.findResourceByName(workerName).findAppointmentsByServiceRecipientName(clientName).get(0);
            assertThat("Expect appointment to exist on the resource", adHocApt, notNullValue());
        }

        log.info("Setup of adhocTest [worker; client; adHocAgreementId; adHocScheduleId; adHocEventRef]: {}; {}; {}, {}, {}",
                workerName, clientName, adHocApt.getAgreementId(), adHocApt.getScheduleId(), adHocApt.getRef());

        logout();

        // check the appointment is allocated to the worker, by checking the
        loginAs(userName, pwd);
        rotaSteps.checkClientReferralIsAvailableOffline(clientName);
        logout();

        loginAsSysadmin();
        var wvm = workerActor.getWorker(workerIdName.getWorkerId()).getBody();
        checkStaffCalendarAppearsOnRota(wvm.jobs.stream().findFirst().orElseThrow().getServiceRecipient().serviceRecipientId);

        ReferralSummaryViewModel svm = referralActor.getReferralSummaryById(referralId).getBody();

        // NB these are dealing with the adHoc appointment
        log.info("Deleting (should fail) first agreement and first schedule appointment");
        checkCannotDeleteAppointmentSchedule(svm.serviceRecipientId, adHocApt.getScheduleId());

        // deallocate ad-hoc
        checkCanUnassignAppointmentFromWorker(clientName, workerName);
        // delete ad-hoc
        log.info("Deleting first agreement and first schedule appointment");
        checkCanDeleteAppointment(svm.serviceRecipientId, adHocApt.getScheduleId());
    }

    protected void checkCanDropAppointmentFromSchedule(RotaAppointmentViewModel apt, DateTime rotaDate) {
        AppointmentActionCommandDto action = new AppointmentActionCommandDto(AppointmentActionCommandDto.OPERATION_DROP,
                apt.getRef(), apt.getServiceRecipientId(), "workers:all", "referrals:all");
        action.dropReasonId = null;
        commandActor.executeCommand(action);

        // Fetch the rota again and check the appointment is shown as unallocated
        Rota rota = rotaActor.getWholeOrgRotaOnDate(rotaDate);
        List<RotaAppointmentViewModel> activities = rota.findDemandByServiceRecipientName(apt.getServiceRecipientName());
        assertThat("Expect appointment to be DROPPED", activities.stream().filter(a -> Recurrence.Status.DROPPED.equals(a.getStatus())).count(), is(1L));
    }

    protected void checkCanReinstateAppointmentToSchedule(RotaAppointmentViewModel apt, DateTime rotaDate) {
        AppointmentActionCommandDto action = new AppointmentActionCommandDto(AppointmentActionCommandDto.OPERATION_REINSTATE,
                apt.getRef(), apt.getServiceRecipientId(), "workers:all", "referrals:all");
        commandActor.executeCommand(action);

        // Fetch the rota again and check the appointment is shown as unallocated
        Rota rota = rotaActor.getWholeOrgRotaOnDate(rotaDate);
        List<RotaAppointmentViewModel> activities = rota.findDemandByServiceRecipientName(apt.getServiceRecipientName());
        assertFalse("Expect appointment to be TENTATIVE", activities.stream().anyMatch(a -> Recurrence.Status.DROPPED.equals(a.getStatus())));
    }

    protected void checkCanUnassignAppointmentFromWorker(String recipientName, String workerName) {
        // TODO: replace with UI interaction when it's finalised to avoid this test getting too fragile now
        Rota rota = rotaActor.getWholeOrgRotaOnDate(now);

        RotaResourceViewModel workerJob = rota.findResourceByName(workerName);
        assertNotNull("Expected worker with name '" + workerName, workerJob);

        List<RotaAppointmentViewModel> appointments = rota.findResourceByName(workerName).findAppointmentsByServiceRecipientName(recipientName);
        assertThat("Expected appointment returned", appointments, iterableWithSize(1));

        // deallocate
        log.info("Unassigning sr-{} from worker-{} for appt-{}", recipientName, workerName, appointments.get(0).getRef());
        AppointmentActionCommandDto action = new AppointmentActionCommandDto(AppointmentActionCommandDto.OPERATION_DEALLOCATE,
                appointments.get(0).getRef(), appointments.get(0).getServiceRecipientId(), "workers:all", "referrals:all");
        action.deallocateResourceId = workerJob.getResourceId().intValue();
        commandActor.executeCommand(action);

        // Fetch the rota again and check the appointment is shown as unallocated
        rota = rotaActor.getWholeOrgRotaOnDate(now);
        List<RotaAppointmentViewModel> appointments2 = rota.findResourceByName(workerName).findAppointmentsByServiceRecipientName(recipientName);
        assertThat("Expected unassigned appointments returned", appointments2, iterableWithSize(0));
        assertThat("Expected demand activities returned", rota.findDemandByServiceRecipientName(recipientName), iterableWithSize(1));
    }

    protected void checkCannotDeleteAppointmentSchedule(int serviceRecipientId, long scheduleId) {
        try {
            checkResultOfDeletingAppointmentSchedule(serviceRecipientId, scheduleId);
            fail("exception expected");
        } catch (HttpServerErrorException e) {
            // do nothing - this is good
        }
    }

    protected void checkCanDeleteAppointment(int serviceRecipientId, long scheduleId) {
        checkResultOfDeletingAppointmentSchedule(serviceRecipientId, scheduleId);
    }

    protected void checkCanDeleteSchedule(RotaAppointmentViewModel apt, int additionalStaff) {//int serviceRecipientId, RecurringEntry.RecurringEntryHandle demandSchedule) {
        var schedules = agreementActor.getAppointmentSchedulesFromAgreement(apt.getServiceRecipientId(),
                JodaToJDKAdapters.localDateTimeToJoda(apt.getStart()).toDateTime()).getBody();
        var schedule = schedules.stream().filter(s -> apt.getScheduleId().equals(s.getScheduleId())).findFirst().orElseThrow();
        log.info("Deleting schedule: id-{}, ref-{}", schedule.getScheduleId(), schedule.getEventRef());
        agreementActor.deleteSchedule(schedule, apt.getServiceRecipientId());
        var rota = rotaActor.getWholeOrgRotaOnDate(now);
        List<RotaAppointmentViewModel> activities = rota.findDemandByServiceRecipientName(apt.getServiceRecipientName());
        assertThat("Expected reinstated appointments don't show", activities, iterableWithSize(additionalStaff));
    }

    void checkResultOfDeletingAppointmentSchedule(int serviceRecipientId, long scheduleId) {
        var schedules = agreementActor.getAppointmentSchedulesFromAgreement(serviceRecipientId, now);
        var schedule = Objects.requireNonNull(schedules.getBody()).get(0);
        assertEquals("Expected scheduleId to match", schedule.getScheduleId(), scheduleId);
        log.info("Deleting schedule: id-{}, ref-{}", scheduleId, schedule.getEventRef());
        agreementActor.deleteSchedule(schedule, serviceRecipientId);

        // Check we can subsequently load the rota
        // any hanging around schedules can be cleared with something like:
        //      select * from appointmentschedules s left join cosmo_item ci on s.calendar_entry_handle=ci.item_uid where ci.item_uid is null;
        rotaActor.getWholeOrgRotaOnDate(now);
    }

    protected ServiceRecipientCalendarEntryCommandViewModel createCommand(int serviceRecipientId, String operation) {
        CalendarEntryCommandViewModel vm = new CalendarEntryCommandViewModel(operation, null);
        return new ServiceRecipientCalendarEntryCommandViewModel(serviceRecipientId, vm);
    }

    void checkStaffCalendarAppearsOnRota(int serviceRecipientId) {
        var vm = createCommand(serviceRecipientId, OPERATION_ADD);
        // Joda-Time uses the ISO standard Monday to Sunday week, so set to Friday for our repeatEveryDays
        org.joda.time.LocalDate start = new org.joda.time.LocalDate().plusWeeks(1).withDayOfWeek(5);
//        DaysOfWeek dow = new DaysOfWeek();
//        dow.setSunday(true);
//        dow.setWednesday(true);

        var end = start.plusDays(4);
        var entryVM = vm.getCalendarEntryViewModel().toBuilder()
                .title(changeNullTo("COVID-24"))
                .startDate(changeNullTo(start))
                // need to set allDay since we don't set a time
                .allDay(changeNullTo(true))
                .endDate(changeNullTo(end))
//                .repeatEveryDays(changeNullTo(dow.daysAttending()))
//                .repeatEveryWeeks(changeNullTo(2)) // trigger recurring
//                .repeatEndDate(changeNullTo(start.plusMonths(6)))
                .eventCategoryId(changeNullTo(CustomEventAbstract.EVENTCATEGORY_SICK))
                .build();
        vm.setCalendarEntryViewModel(entryVM);
        Result result = commandActor.executeCommand(vm.getCalendarEntryViewModelParent()).getBody();
        assertTrue(result.isCommandSuccessful());
        assertNotNull(result.getId());

        checkAppointmentFoundFor(serviceRecipientId, start, end);
        checkAppointmentFoundFor(serviceRecipientId, start.plusDays(2), end);
        checkAppointmentFoundFor(serviceRecipientId, start.plusDays(3), end);
        // TODO: Check if this should pass checkAppointmentFoundFor(serviceRecipientId, start.plusDays(4));
    }

    private void checkAppointmentFoundFor(int serviceRecipientId, org.joda.time.LocalDate onDate, org.joda.time.LocalDate expectedEndDate) {
        var rota = rotaActor.getWholeOrgRotaOnDate(localDateToJDk(onDate).atStartOfDay());

        var matches = rota.getResources().stream()
                .filter(r -> r.getServiceRecipientId() == serviceRecipientId).toList();

        Assertions.assertThat(matches).describedAs("one matching worker").hasSize(1);
        Assertions.assertThat(matches.get(0).getAppointments()).describedAs("one event appears on rota").hasSize(1);
        Condition<? super RotaAppointmentViewModel> correctEndDate =
                new Condition<>(a -> a.getEnd().toLocalDate().equals(localDateToJDk(expectedEndDate)), "correct expectedEndDate date");
        Assertions.assertThat(matches.get(0).getAppointments()).first().has(correctEndDate);
    }
}
